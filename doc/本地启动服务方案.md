# JH-Loan-Cash-Capital 本地启动服务方案

## 📋 项目概述

JH-Loan-Cash-Capital 是一个微服务项目，包含两个主要模块：
- **capital-core**: 核心业务服务 (端口: 8001)
- **capital-batch**: 批处理任务服务 (端口: 8100)

## 🏗️ 中间件使用分析

### 公用服务 (团队共享，不需要本地部署)
| 中间件 | 用途 | 使用场景 | 备注 |
|--------|------|----------|------|
| **MySQL** | 关系型数据库 | 业务数据存储 | capital-core和capital-batch都需要 |
| **Redis** | 缓存/分布式锁 | 缓存、分布式锁 | 使用Redisson客户端 |
| **Apollo** | 配置中心 | 动态配置管理 | 现有地址: http://47.117.21.34:8080 |
| **RabbitMQ** | 消息队列 | 异步消息处理 | capital-core有消息监听器 |
| **Eureka** | 服务注册发现 | 微服务通信 | 服务间调用必需 |

### 本地启动服务 (需要在本地启动)
| 服务 | 端口 | 必需性 | 说明 |
|------|------|--------|------|
| **capital-core** | 8001 | 必需 | 核心业务服务 |
| **capital-batch** | 8100 | 可选 | 批处理任务服务，用于定时任务 |

### 可选服务 (按业务需要)
| 中间件 | 用途 | 何时需要 | 部署方式 |
|--------|------|----------|----------|
| **XXL-Job** | 分布式任务调度 | capital-batch需要执行定时任务时 | 公用或本地 |
| **SFTP服务器** | 文件传输 | 业务功能涉及文件上传下载时 | 公用 |

## 🚀 启动方案

### 启动顺序
1. **公用服务**: 确保MySQL、Redis、Apollo、RabbitMQ、Eureka可访问
2. **本地服务**: 启动capital-core (必需)，capital-batch (可选)

### 配置要点
- **Apollo配置**: 使用现有Apollo (http://47.117.21.34:8080)，配置本地数据库和中间件连接信息
- **备选方案**: 如果Apollo不可用，可创建本地配置文件禁用Apollo

### 启动命令
```bash
# capital-core (必需)
cd capital-core && mvn spring-boot:run

# capital-batch (可选)
cd capital-batch && mvn spring-boot:run
```

## 📝 检查清单

### 公用服务确认
- [ ] MySQL 可连接
- [ ] Redis 可连接
- [ ] Apollo 配置中心可访问
- [ ] RabbitMQ 可连接
- [ ] Eureka 服务注册中心可访问

### 本地服务启动
- [ ] capital-core 启动成功 (端口8001)
- [ ] capital-batch 启动成功 (端口8100，可选)
- [ ] 服务健康检查正常: http://localhost:8001/actuator/health
