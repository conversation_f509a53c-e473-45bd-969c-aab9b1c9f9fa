# JH-Loan-Cash-Capital 本地启动服务方案

## 📋 项目概述

JH-Loan-Cash-Capital 是一个基于 Spring Boot 3.2.1 和 Spring Cloud 2023.0.0 的微服务项目，包含两个主要模块：
- **capital-core**: 核心业务服务 (端口: 8001)
- **capital-batch**: 批处理任务服务 (端口: 8100)

## 🏗️ 服务架构分析

### 公用服务 (共享基础设施)
- **MySQL**: 关系型数据库存储
- **Redis**: 缓存和分布式锁
- **Apollo**: 配置中心
- **RabbitMQ**: 消息队列 (capital-core有MQ监听器)
- **Eureka**: 服务注册与发现 (微服务间通信必需)

### 本地启动服务 (应用服务)
- **capital-core**: 核心业务服务
- **capital-batch**: 批处理任务服务 (可选，用于定时任务)

### 可选服务 (按需启动)
- **XXL-Job**: 分布式任务调度平台 (仅capital-batch需要)
- **SFTP服务器**: 文件传输 (业务功能需要时)

## 🚀 本地启动方案

### 第一步：启动公用服务

#### 1.1 使用 Docker 启动基础中间件
创建 `docker-compose.yml` 文件：

```yaml
version: '3.8'
services:
  mysql:
    image: mysql:8.0
    container_name: mysql-local
    ports:
      - "3306:3306"
    environment:
      MYSQL_ROOT_PASSWORD: root123
      MYSQL_DATABASE: capital_core
    volumes:
      - mysql_data:/var/lib/mysql
    command: --default-authentication-plugin=mysql_native_password

  redis:
    image: redis:6.2-alpine
    container_name: redis-local
    ports:
      - "6379:6379"
    command: redis-server --requirepass redis123

  rabbitmq:
    image: rabbitmq:3.8-management
    container_name: rabbitmq-local
    ports:
      - "5672:5672"
      - "15672:15672"
    environment:
      RABBITMQ_DEFAULT_USER: admin
      RABBITMQ_DEFAULT_PASS: admin123

  eureka:
    image: springcloud/eureka
    container_name: eureka-local
    ports:
      - "8761:8761"

volumes:
  mysql_data:
```

启动命令：
```bash
docker-compose up -d
```

#### 1.2 Apollo 配置中心
**选项A: 使用现有远程Apollo**
```properties
# 保持现有配置
apollo.meta=http://************:8080
app.id=capital-core
```

**选项B: 本地Docker启动Apollo (可选)**
```bash
# 如果需要完全本地化，可以启动本地Apollo
docker run -d --name apollo-local -p 8080:8080 apolloconfig/apollo-quick-start
```

### 第二步：配置本地启动服务

#### 2.1 数据库初始化
```sql
-- 连接MySQL创建数据库
CREATE DATABASE capital_core DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE capital_batch DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户 (可选)
CREATE USER 'capital_user'@'localhost' IDENTIFIED BY 'capital123';
GRANT ALL PRIVILEGES ON capital_core.* TO 'capital_user'@'localhost';
GRANT ALL PRIVILEGES ON capital_batch.* TO 'capital_user'@'localhost';
FLUSH PRIVILEGES;
```

#### 2.2 Apollo配置 (推荐使用现有Apollo)
在Apollo配置中心为本地环境添加以下配置：

**应用: capital-core**
```properties
# 数据库配置
spring.datasource.url=******************************************************************************************************************************************************
spring.datasource.username=root
spring.datasource.password=root123

# Redis配置
spring.data.redis.host=localhost
spring.data.redis.port=6379
spring.data.redis.password=redis123
spring.data.redis.database=0

# RabbitMQ配置
spring.rabbitmq.host=localhost
spring.rabbitmq.port=5672
spring.rabbitmq.username=admin
spring.rabbitmq.password=admin123

# Eureka配置
eureka.client.service-url.defaultZone=http://localhost:8761/eureka/
```

**应用: capital-batch**
```properties
# 数据库配置 (同上)
# XXL-Job配置 (如果需要)
xxl.job.admin.addresses=http://localhost:8080/xxl-job-admin
xxl.job.executor.appname=capital-batch-local
```

#### 2.3 本地配置文件 (备选方案)
如果不使用Apollo，可以创建本地配置文件：

`capital-core/src/main/resources/application-local.properties`:
```properties
# 禁用Apollo
apollo.bootstrap.enabled=false
spring.config.import=

# 基础配置
server.port=8001
spring.application.name=capital-core-service

# 数据库配置
spring.datasource.url=******************************************************************************************************************************************************
spring.datasource.username=root
spring.datasource.password=root123

# Redis配置
spring.data.redis.host=localhost
spring.data.redis.port=6379
spring.data.redis.password=redis123

# RabbitMQ配置
spring.rabbitmq.host=localhost
spring.rabbitmq.port=5672
spring.rabbitmq.username=admin
spring.rabbitmq.password=admin123

# Eureka配置
eureka.client.service-url.defaultZone=http://localhost:8761/eureka/

# 日志配置
logging.level.com.jinghang.capital=DEBUG
```

### 第三步：启动应用服务

#### 3.1 启动 capital-core (必需)
```bash
# 方式1: 使用Maven
cd capital-core
mvn spring-boot:run

# 方式2: 使用本地配置
mvn spring-boot:run -Dspring-boot.run.profiles=local

# 方式3: 使用IDE
# 在IDEA中设置Active profiles: local
```

#### 3.2 启动 capital-batch (可选)
```bash
cd capital-batch
mvn spring-boot:run
```

## 🔧 开发调试技巧

### 1. 服务健康检查
```bash
# 检查服务状态
curl http://localhost:8001/actuator/health
curl http://localhost:8100/actuator/health

# 检查Eureka注册状态
curl http://localhost:8761/eureka/apps

# 检查RabbitMQ管理界面
# 访问: http://localhost:15672 (admin/admin123)
```

### 2. IDE配置
- **IDEA**: 配置 Spring Boot 运行配置，设置 Active profiles 为 `local`
- **Eclipse**: 在 Run Configuration 中设置 VM arguments: `-Dspring.profiles.active=local`

### 3. 热部署
项目已包含 `spring-boot-devtools`，修改代码后自动重启。

### 4. API测试
- **Actuator**: http://localhost:8001/actuator/health
- **Eureka**: http://localhost:8761

## 🚨 常见问题解决

### 1. 端口冲突
```bash
# Windows查看端口占用
netstat -ano | findstr :8001

# Linux/Mac查看端口占用
lsof -i :8001

# 修改端口 (在配置文件中)
server.port=8002
```

### 2. 数据库连接失败
```bash
# 检查MySQL容器状态
docker ps | grep mysql
docker logs mysql-local

# 测试数据库连接
mysql -h localhost -P 3306 -u root -proot123
```

### 3. Redis连接失败
```bash
# 检查Redis容器状态
docker ps | grep redis

# 测试Redis连接
redis-cli -h localhost -p 6379 -a redis123 ping
```

### 4. Apollo配置问题
```bash
# 检查Apollo连接
curl http://************:8080/services/config

# 本地禁用Apollo (在application-local.properties中)
apollo.bootstrap.enabled=false
```

### 5. Eureka注册失败
```bash
# 检查Eureka服务
curl http://localhost:8761/eureka/apps

# 临时禁用Eureka
eureka.client.enabled=false
```

## 📝 启动检查清单

### 环境检查
- [ ] Java 17 已安装
- [ ] Maven 3.6+ 已配置
- [ ] Docker 已安装并运行

### 公用服务检查
- [ ] MySQL 容器启动 (端口3306)
- [ ] Redis 容器启动 (端口6379)
- [ ] RabbitMQ 容器启动 (端口5672/15672)
- [ ] Eureka 容器启动 (端口8761)
- [ ] Apollo 配置中心可访问

### 应用服务检查
- [ ] capital-core 启动成功 (端口8001)
- [ ] capital-batch 启动成功 (端口8100，可选)
- [ ] 服务注册到Eureka成功
- [ ] 健康检查接口正常

## 🎯 推荐启动顺序

### 第一阶段：公用服务
1. **Docker服务**: `docker-compose up -d`
2. **数据库初始化**: 创建数据库和表结构
3. **Apollo配置**: 确认配置中心连接正常

### 第二阶段：应用服务
1. **capital-core**: 核心业务服务 (必需)
2. **capital-batch**: 批处理服务 (可选)

### 验证启动成功
```bash
# 检查所有服务状态
docker ps
curl http://localhost:8001/actuator/health
curl http://localhost:8761/eureka/apps
```

现在你可以开始本地开发了！🚀
