# JH-Loan-Cash 系统本地启动服务方案

## 📋 系统架构概述

JH-Loan-Cash 是一个完整的微服务借贷系统，包含以下主要服务：

### 🎯 核心业务服务
| 服务名 | 端口 | 服务标识 | Apollo App ID | 说明 |
|--------|------|----------|---------------|------|
| **cash-flow** | 8001 | cash-flow | loan-cash-flow-paipai | 主业务流程服务 |
| **capital-core** | 8001 | capital-core-service | capital-core | 资金核心服务 |
| **capital-batch** | 8100 | fin-batch | capital-batch | 批处理任务服务 |
| **cash-manage** | - | cash-manage | cash-flow-manage | 后台管理服务 |
| **sing-service** | 8081 | sing-service | sing-service | 签章服务 |

## 🏗️ 中间件使用分析

### 公用服务 (团队共享，不需要本地部署)
| 中间件 | 用途 | 使用场景 | 风险评估 |
|--------|------|----------|----------|
| **MySQL** | 关系型数据库 | 业务数据存储 | ✅ 可共享，数据库隔离 |
| **Redis** | 缓存/分布式锁 | 缓存、分布式锁 | ✅ 可共享，key前缀隔离 |
| **Apollo** | 配置中心 | 动态配置管理 | ✅ 可共享，应用ID隔离 |
| **RabbitMQ** | 消息队列 | 异步消息处理 | ⚠️ **需要隔离** |
| **Eureka** | 服务注册发现 | 微服务通信 | ⚠️ **需要隔离** |

### 本地启动服务 (需要在本地启动)
| 服务 | 端口 | 必需性 | 说明 |
|------|------|--------|------|
| **capital-core** | 8001 | 必需 | 资金核心服务 |
| **capital-batch** | 8100 | 可选 | 批处理任务服务 |

### 可选服务 (按业务需要)
| 中间件 | 用途 | 何时需要 | 部署方式 |
|--------|------|----------|----------|
| **XXL-Job** | 分布式任务调度 | capital-batch需要执行定时任务时 | 公用或本地 |
| **SFTP服务器** | 文件传输 | 业务功能涉及文件上传下载时 | 公用 |

## 🚨 关键问题分析

### RabbitMQ 隔离问题
**问题**: 系统大量使用MQ，包含以下关键队列：
- `risk.apply` - 风控申请
- `credit.apply` - 授信申请
- `loan.apply` - 放款申请
- `repay.*` - 还款相关
- `sms.send` - 短信发送

**风险**: 如果多个开发者连接同一个RabbitMQ，会导致：
- 消息被其他开发者消费
- 业务流程被打断
- 调试困难

### Eureka 隔离分析
**服务调用关系**:
- `cash-flow` → `capital-core-service`
- `cash-flow` → `sing-service`
- `cash-manage` → `cash-flow`

**隔离可行性**: Eureka可以公用，因为：
- 服务名称不同，天然隔离
- 本地端口不同，不会冲突
- OpenFeign通过服务名+负载均衡调用，只会路由到对应服务

## 🚀 本地启动方案

### 中间件部署策略
| 中间件 | 部署方式 | 原因 |
|--------|----------|------|
| **MySQL** | 可公用 | 数据库隔离简单 |
| **Redis** | 可公用 | key前缀隔离 |
| **Apollo** | 公用 | 应用ID已隔离 |
| **RabbitMQ** | 本地部署 | 避免消息冲突 |
| **Eureka** | 可公用 | 服务名+端口天然隔离 |

### 本地启动服务
- **capital-core** (8001) - 必需
- **capital-batch** (8100) - 可选

## 🎯 启动步骤

### 第一步：启动本地中间件
只需要启动RabbitMQ，其他中间件可以公用：

```yaml
# docker-compose.yml (最小化版本)
version: '3.8'
services:
  rabbitmq:
    image: rabbitmq:3.8-management
    container_name: rabbitmq-local
    ports:
      - "5672:5672"
      - "15672:15672"
    environment:
      RABBITMQ_DEFAULT_USER: admin
      RABBITMQ_DEFAULT_PASS: admin123
```

如果需要完全本地化，可以启动全套：
```yaml
# docker-compose.yml (完整版本)
version: '3.8'
services:
  mysql:
    image: mysql:8.0
    container_name: mysql-local
    ports:
      - "3306:3306"
    environment:
      MYSQL_ROOT_PASSWORD: root123
      MYSQL_DATABASE: capital_core
    volumes:
      - mysql_data:/var/lib/mysql

  redis:
    image: redis:6.2-alpine
    container_name: redis-local
    ports:
      - "6379:6379"
    command: redis-server --requirepass redis123

  rabbitmq:
    image: rabbitmq:3.8-management
    container_name: rabbitmq-local
    ports:
      - "5672:5672"
      - "15672:15672"
    environment:
      RABBITMQ_DEFAULT_USER: admin
      RABBITMQ_DEFAULT_PASS: admin123

volumes:
  mysql_data:
```

启动命令：
```bash
docker-compose up -d
```

### 第二步：配置Apollo
在Apollo配置中心为本地环境添加配置：

**应用: capital-core**
```properties
# RabbitMQ配置 (必需本地)
spring.rabbitmq.host=localhost
spring.rabbitmq.port=5672
spring.rabbitmq.username=admin
spring.rabbitmq.password=admin123

# Eureka配置 (公用)
eureka.client.service-url.defaultZone=http://公用Eureka地址:8761/eureka/

# 数据库配置 (可选择本地或公用)
# 本地MySQL
spring.datasource.url=******************************************************************************************************************************************************
spring.datasource.username=root
spring.datasource.password=root123

# 或者公用MySQL (使用不同数据库名)
# spring.datasource.url=****************************************************_张三?...
# spring.datasource.username=dev_user
# spring.datasource.password=dev_password

# Redis配置 (可选择本地或公用)
# 本地Redis
spring.data.redis.host=localhost
spring.data.redis.port=6379
spring.data.redis.password=redis123
spring.data.redis.database=0

# 或者公用Redis (使用key前缀隔离)
# spring.data.redis.host=公用Redis地址
# spring.data.redis.port=6379
# spring.data.redis.password=公用密码
# spring.data.redis.key-prefix=dev-张三:
```

### 第三步：启动应用服务
```bash
# capital-core (必需)
cd capital-core && mvn spring-boot:run

# capital-batch (可选)
cd capital-batch && mvn spring-boot:run
```

## 📝 检查清单

### 基础环境
- [ ] Java 17 已安装
- [ ] Maven 3.6+ 已配置
- [ ] Docker 已安装 (用于启动RabbitMQ)

### 中间件服务
- [ ] **RabbitMQ 本地启动成功** (端口5672/15672) - 必需本地部署
- [ ] **MySQL 连接正常** (可公用或本地)
- [ ] **Redis 连接正常** (可公用或本地)
- [ ] **Eureka 连接正常** (公用) - 服务注册中心
- [ ] **Apollo 配置中心可访问** (公用) - 配置管理

### 应用服务启动
- [ ] capital-core 启动成功 (端口8001)
- [ ] capital-batch 启动成功 (端口8100，可选)
- [ ] 服务注册到Eureka成功
- [ ] 健康检查正常: http://localhost:8001/actuator/health

### 配置验证
- [ ] Apollo中已配置本地RabbitMQ连接信息
- [ ] Apollo中已配置数据库连接信息 (本地或公用)
- [ ] Apollo中已配置Redis连接信息 (本地或公用)
- [ ] Apollo中已配置公用Eureka地址

## ⚠️ 重要提醒

### 🔴 必须本地部署
- **RabbitMQ**: 避免消息被其他开发者消费，业务流程冲突

### 🟢 可以公用
- **Eureka**: 服务名+端口天然隔离，无冲突风险
- **MySQL**: 通过不同数据库名隔离 (如: `capital_core_dev_张三`)
- **Redis**: 通过key前缀隔离 (如: `dev-张三:`)
- **Apollo**: 应用ID已经做了隔离

## 🔍 服务验证

启动完成后，访问以下地址验证服务状态：

- **RabbitMQ管理界面**: http://localhost:15672 (admin/admin123)
- **capital-core健康检查**: http://localhost:8001/actuator/health
- **capital-batch健康检查**: http://localhost:8100/actuator/health (如果启动)

## 💡 方案优势

### 🎯 最小化部署
- **只需启动RabbitMQ**: 大幅节省本地资源
- **其他中间件公用**: 减少维护成本
- **配置简单**: 无需复杂的隔离配置

### 🛡️ 有效隔离
- **消息队列完全隔离**: 避免业务流程干扰
- **服务调用天然隔离**: 服务名+端口确保路由正确
- **数据存储可选隔离**: 根据需要选择本地或公用

### ⚡ 快速启动
```bash
# 1. 启动RabbitMQ
docker run -d --name rabbitmq-local -p 5672:5672 -p 15672:15672 \
  -e RABBITMQ_DEFAULT_USER=admin -e RABBITMQ_DEFAULT_PASS=admin123 \
  rabbitmq:3.8-management

# 2. 启动应用
cd capital-core && mvn spring-boot:run
```
