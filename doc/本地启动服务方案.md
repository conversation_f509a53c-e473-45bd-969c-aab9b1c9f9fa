# JH-Loan-Cash 系统本地启动服务方案

## 📋 系统架构概述

JH-Loan-Cash 是一个完整的微服务借贷系统，包含以下主要服务：

### 🎯 核心业务服务
| 服务名 | 端口 | 服务标识 | Apollo App ID | 说明 |
|--------|------|----------|---------------|------|
| **cash-flow** | 8001 | cash-flow | loan-cash-flow-paipai | 主业务流程服务 |
| **capital-core** | 8001 | capital-core-service | capital-core | 资金核心服务 |
| **capital-batch** | 8100 | fin-batch | capital-batch | 批处理任务服务 |
| **cash-manage** | - | cash-manage | cash-flow-manage | 后台管理服务 |
| **sing-service** | 8081 | sing-service | sing-service | 签章服务 |

## 🏗️ 中间件使用分析

### 公用服务 (团队共享，不需要本地部署)
| 中间件 | 用途 | 使用场景 | 风险评估 |
|--------|------|----------|----------|
| **MySQL** | 关系型数据库 | 业务数据存储 | ✅ 可共享，数据库隔离 |
| **Redis** | 缓存/分布式锁 | 缓存、分布式锁 | ✅ 可共享，key前缀隔离 |
| **Apollo** | 配置中心 | 动态配置管理 | ✅ 可共享，应用ID隔离 |
| **RabbitMQ** | 消息队列 | 异步消息处理 | ⚠️ **需要隔离** |
| **Eureka** | 服务注册发现 | 微服务通信 | ⚠️ **需要隔离** |

### 本地启动服务 (需要在本地启动)
| 服务 | 端口 | 必需性 | 说明 |
|------|------|--------|------|
| **capital-core** | 8001 | 必需 | 资金核心服务 |
| **capital-batch** | 8100 | 可选 | 批处理任务服务 |

### 可选服务 (按业务需要)
| 中间件 | 用途 | 何时需要 | 部署方式 |
|--------|------|----------|----------|
| **XXL-Job** | 分布式任务调度 | capital-batch需要执行定时任务时 | 公用或本地 |
| **SFTP服务器** | 文件传输 | 业务功能涉及文件上传下载时 | 公用 |

## 🚨 关键问题分析

### RabbitMQ 隔离问题
**问题**: 系统大量使用MQ，包含以下关键队列：
- `risk.apply` - 风控申请
- `credit.apply` - 授信申请
- `loan.apply` - 放款申请
- `repay.*` - 还款相关
- `sms.send` - 短信发送

**风险**: 如果多个开发者连接同一个RabbitMQ，会导致：
- 消息被其他开发者消费
- 业务流程被打断
- 调试困难

**解决方案**:
- **方案A**: 本地启动独立RabbitMQ
- **方案B**: 使用队列名前缀隔离 (如: `dev-张三-risk.apply`)

### Eureka 隔离问题
**问题**: 服务间大量使用OpenFeign调用：
- `cash-flow` → `capital-core-service`
- `cash-flow` → `sing-service`
- `cash-manage` → `cash-flow`

**风险**: 如果连接同一个Eureka，会导致：
- 服务调用路由到其他开发者实例
- 负载均衡混乱
- 调试困难

**解决方案**:
- **方案A**: 本地启动独立Eureka
- **方案B**: 使用不同的Eureka Zone隔离

## 🚀 推荐启动方案

### 方案一：完全本地化 (推荐)
```bash
# 1. 本地中间件 (需要启动)
- MySQL (本地)
- Redis (本地)
- RabbitMQ (本地) - 避免消息冲突
- Eureka (本地) - 避免服务调用冲突

# 2. 公用服务
- Apollo (公用) - 配置隔离良好

# 3. 本地应用服务
- capital-core (8001)
- capital-batch (8100, 可选)
```

### 方案二：混合模式 (谨慎使用)
```bash
# 1. 公用中间件 (需要配置隔离)
- MySQL (公用) - 使用不同数据库
- Redis (公用) - 使用不同database或key前缀
- Apollo (公用) - 应用ID已隔离

# 2. 本地中间件 (避免冲突)
- RabbitMQ (本地) - 必须本地，避免消息冲突
- Eureka (本地) - 必须本地，避免服务调用冲突

# 3. 本地应用服务
- capital-core (8001)
- capital-batch (8100, 可选)
```

## 🎯 启动步骤

### 第一步：启动基础中间件
```bash
# MySQL + Redis + RabbitMQ + Eureka
# 建议使用Docker Compose一键启动
```

### 第二步：配置Apollo
```bash
# 在Apollo中为本地环境配置：
# - 数据库连接 (本地MySQL)
# - Redis连接 (本地Redis)
# - RabbitMQ连接 (本地RabbitMQ)
# - Eureka连接 (本地Eureka)
```

### 第三步：启动应用服务
```bash
# capital-core (必需)
cd capital-core && mvn spring-boot:run

# capital-batch (可选)
cd capital-batch && mvn spring-boot:run
```

## 📝 检查清单

### 基础环境
- [ ] Java 17 已安装
- [ ] Maven 3.6+ 已配置

### 中间件服务 (推荐本地启动)
- [ ] MySQL 启动成功 (端口3306)
- [ ] Redis 启动成功 (端口6379)
- [ ] RabbitMQ 启动成功 (端口5672/15672) - **重要：避免消息冲突**
- [ ] Eureka 启动成功 (端口8761) - **重要：避免服务调用冲突**
- [ ] Apollo 配置中心可访问 (公用)

### 应用服务启动
- [ ] capital-core 启动成功 (端口8001)
- [ ] capital-batch 启动成功 (端口8100，可选)
- [ ] 服务注册到Eureka成功
- [ ] 健康检查正常: http://localhost:8001/actuator/health

## 🔧 隔离方案成本分析

### RabbitMQ 队列前缀隔离方案

#### 实现成本：**中等**
**需要修改的地方：**
1. **队列常量定义** (1处)：`RabbitConfig.Queues` 接口
2. **Exchange常量定义** (1处)：`RabbitConfig.Exchanges` 接口
3. **队列Bean定义** (约50个)：所有 `@Bean Queue` 方法
4. **绑定关系** (约50个)：所有 `@Bean Binding` 方法
5. **消息监听器** (约30个)：所有 `@RabbitListener` 注解

**具体实现：**
```java
// 1. 添加配置
@Value("${dev.queue.prefix:}")
private String queuePrefix;

// 2. 修改队列常量
public interface Queues {
    String RISK_APPLY = "${dev.queue.prefix}risk.apply";  // 原: "risk.apply"
    String CREDIT_APPLY = "${dev.queue.prefix}credit.apply";
    // ... 其他50+个队列
}

// 3. 修改队列Bean定义
@Bean
public Queue riskApplyQueue() {
    return QueueBuilder.durable(queuePrefix + Queues.RISK_APPLY).build();
}

// 4. 修改监听器
@RabbitListener(queues = "#{@environment.getProperty('dev.queue.prefix', '')}risk.apply")
public void listenRiskApply(Message message, Channel channel) {
    // 业务逻辑不变
}
```

**配置方式：**
```properties
# 开发者A
dev.queue.prefix=dev-zhangsan-

# 开发者B
dev.queue.prefix=dev-lisi-

# 生产环境
dev.queue.prefix=
```

### Eureka Zone隔离方案

#### 实现成本：**低**
**需要修改的地方：**
1. **Eureka配置** (各服务的application.properties)

**具体实现：**
```properties
# 开发者A的配置
eureka.instance.metadata-map.zone=dev-zhangsan
eureka.client.prefer-same-zone-eureka=true

# 开发者B的配置
eureka.instance.metadata-map.zone=dev-lisi
eureka.client.prefer-same-zone-eureka=true

# 生产环境
eureka.instance.metadata-map.zone=production
```

**OpenFeign自动支持Zone隔离，无需修改代码**

## 💰 成本对比总结

| 方案 | RabbitMQ | Eureka | 总体成本 | 推荐度 |
|------|----------|--------|----------|--------|
| **完全本地化** | 无需修改代码 | 无需修改代码 | **低** | ⭐⭐⭐⭐⭐ |
| **前缀/Zone隔离** | 需修改100+处代码 | 只需修改配置 | **中** | ⭐⭐⭐ |

## ⚠️ 最终建议

**推荐完全本地化方案**，原因：
1. **RabbitMQ隔离成本高** - 需要修改100+处代码，容易出错
2. **维护复杂** - 每次新增队列都要考虑前缀问题
3. **本地Docker成本低** - 一键启动，资源占用不大
4. **完全隔离** - 避免任何潜在冲突

如果资源受限，可以考虑：
- **MySQL + Redis**: 公用 (成本低，隔离简单)
- **RabbitMQ + Eureka**: 本地 (避免复杂的代码修改)
