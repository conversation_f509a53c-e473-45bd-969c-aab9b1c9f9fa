# JH-Loan-Cash 系统本地启动服务方案

## 📋 系统架构概述

JH-Loan-Cash 是一个完整的微服务借贷系统，包含以下主要服务：

### 🎯 核心业务服务
| 服务名 | 端口 | 服务标识 | Apollo App ID | 说明 |
|--------|------|----------|---------------|------|
| **cash-flow** | 8001 | cash-flow | loan-cash-flow-paipai | 主业务流程服务 |
| **capital-core** | 8001 | capital-core-service | capital-core | 资金核心服务 |
| **capital-batch** | 8100 | fin-batch | capital-batch | 批处理任务服务 |
| **cash-manage** | - | cash-manage | cash-flow-manage | 后台管理服务 |
| **sing-service** | 8081 | sing-service | sing-service | 签章服务 |

## 🏗️ 中间件使用分析

### 公用服务 (团队共享，不需要本地部署)
| 中间件 | 用途 | 使用场景 | 风险评估 |
|--------|------|----------|----------|
| **MySQL** | 关系型数据库 | 业务数据存储 | ✅ 可共享，数据库隔离 |
| **Redis** | 缓存/分布式锁 | 缓存、分布式锁 | ✅ 可共享，key前缀隔离 |
| **Apollo** | 配置中心 | 动态配置管理 | ✅ 可共享，应用ID隔离 |
| **RabbitMQ** | 消息队列 | 异步消息处理 | ⚠️ **需要隔离** |
| **Eureka** | 服务注册发现 | 微服务通信 | ⚠️ **需要隔离** |

### 本地启动服务 (需要在本地启动)
| 服务 | 端口 | 必需性 | 说明 |
|------|------|--------|------|
| **capital-core** | 8001 | 必需 | 资金核心服务 |
| **capital-batch** | 8100 | 可选 | 批处理任务服务 |

### 可选服务 (按业务需要)
| 中间件 | 用途 | 何时需要 | 部署方式 |
|--------|------|----------|----------|
| **XXL-Job** | 分布式任务调度 | capital-batch需要执行定时任务时 | 公用或本地 |
| **SFTP服务器** | 文件传输 | 业务功能涉及文件上传下载时 | 公用 |

## 🚨 关键问题分析

### RabbitMQ 隔离问题
**问题**: 系统大量使用MQ，包含以下关键队列：
- `risk.apply` - 风控申请
- `credit.apply` - 授信申请
- `loan.apply` - 放款申请
- `repay.*` - 还款相关
- `sms.send` - 短信发送

**风险**: 如果多个开发者连接同一个RabbitMQ，会导致：
- 消息被其他开发者消费
- 业务流程被打断
- 调试困难

**解决方案**:
- **方案A**: 本地启动独立RabbitMQ
- **方案B**: 使用队列名前缀隔离 (如: `dev-张三-risk.apply`)

### Eureka 隔离问题
**问题**: 服务间大量使用OpenFeign调用：
- `cash-flow` → `capital-core-service`
- `cash-flow` → `sing-service`
- `cash-manage` → `cash-flow`

**风险**: 如果连接同一个Eureka，会导致：
- 服务调用路由到其他开发者实例
- 负载均衡混乱
- 调试困难

**解决方案**:
- **方案A**: 本地启动独立Eureka
- **方案B**: 使用不同的Eureka Zone隔离

## 🚀 推荐启动方案

### 方案一：完全本地化 (推荐)
```bash
# 1. 本地中间件 (需要启动)
- MySQL (本地)
- Redis (本地)
- RabbitMQ (本地) - 避免消息冲突
- Eureka (本地) - 避免服务调用冲突

# 2. 公用服务
- Apollo (公用) - 配置隔离良好

# 3. 本地应用服务
- capital-core (8001)
- capital-batch (8100, 可选)
```

### 方案二：混合模式 (谨慎使用)
```bash
# 1. 公用中间件 (需要配置隔离)
- MySQL (公用) - 使用不同数据库
- Redis (公用) - 使用不同database或key前缀
- Apollo (公用) - 应用ID已隔离

# 2. 本地中间件 (避免冲突)
- RabbitMQ (本地) - 必须本地，避免消息冲突
- Eureka (本地) - 必须本地，避免服务调用冲突

# 3. 本地应用服务
- capital-core (8001)
- capital-batch (8100, 可选)
```

## 🎯 启动步骤

### 第一步：启动基础中间件
```bash
# MySQL + Redis + RabbitMQ + Eureka
# 建议使用Docker Compose一键启动
```

### 第二步：配置Apollo
```bash
# 在Apollo中为本地环境配置：
# - 数据库连接 (本地MySQL)
# - Redis连接 (本地Redis)
# - RabbitMQ连接 (本地RabbitMQ)
# - Eureka连接 (本地Eureka)
```

### 第三步：启动应用服务
```bash
# capital-core (必需)
cd capital-core && mvn spring-boot:run

# capital-batch (可选)
cd capital-batch && mvn spring-boot:run
```

## 📝 检查清单

### 基础环境
- [ ] Java 17 已安装
- [ ] Maven 3.6+ 已配置

### 中间件服务 (推荐本地启动)
- [ ] MySQL 启动成功 (端口3306)
- [ ] Redis 启动成功 (端口6379)
- [ ] RabbitMQ 启动成功 (端口5672/15672) - **重要：避免消息冲突**
- [ ] Eureka 启动成功 (端口8761) - **重要：避免服务调用冲突**
- [ ] Apollo 配置中心可访问 (公用)

### 应用服务启动
- [ ] capital-core 启动成功 (端口8001)
- [ ] capital-batch 启动成功 (端口8100，可选)
- [ ] 服务注册到Eureka成功
- [ ] 健康检查正常: http://localhost:8001/actuator/health

## ⚠️ 重要提醒

1. **RabbitMQ必须本地启动** - 系统大量使用消息队列，共享会导致消息被其他开发者消费
2. **Eureka必须本地启动** - 服务间调用频繁，共享会导致请求路由到其他开发者实例
3. **MySQL和Redis可以共享** - 通过数据库名和key前缀可以有效隔离
4. **Apollo可以共享** - 应用ID已经做了隔离

这样的配置能确保你的本地开发环境完全独立，不会影响其他开发者的工作。
