# JH-Loan-Cash-Capital 本地启动服务方案

## 📋 项目概述

JH-Loan-Cash-Capital 是一个基于 Spring Boot 3.2.1 和 Spring Cloud 2023.0.0 的微服务项目，包含两个主要模块：
- **capital-core**: 核心业务服务 (端口: 8001)
- **capital-batch**: 批处理任务服务 (端口: 8100)

## 🛠️ 技术栈和中间件

### 核心框架
- **Java**: 17
- **Spring Boot**: 3.2.1
- **Spring Cloud**: 2023.0.0
- **Maven**: 项目构建工具

### 数据存储
- **MySQL**: 关系型数据库 (JPA + MyBatis Plus)
- **Redis**: 缓存和分布式锁 (Redisson 3.25.2)

### 服务治理
- **Eureka**: 服务注册与发现
- **OpenFeign**: 服务间通信
- **Apollo**: 配置中心 (2.2.0)

### 消息队列
- **RabbitMQ**: 异步消息处理

### 任务调度
- **XXL-Job**: 分布式任务调度 (仅 capital-batch)

### 其他组件
- **SFTP**: 文件传输 (JSch 0.2.17)
- **阿里云OSS**: 对象存储
- **华为云OBS**: 对象存储
- **蚂蚁数科**: 风控服务集成

## 🚀 本地启动方案

### 方案一：完整本地环境 (推荐)

#### 1. 环境准备

##### 1.1 基础软件安装
```bash
# Java 17
java -version

# Maven 3.6+
mvn -version

# MySQL 8.0+
mysql --version

# Redis 6.0+
redis-server --version

# RabbitMQ 3.8+
rabbitmq-server
```

##### 1.2 使用 Docker 快速启动中间件
```bash
# 创建 docker-compose.yml
version: '3.8'
services:
  mysql:
    image: mysql:8.0
    container_name: mysql-local
    ports:
      - "3306:3306"
    environment:
      MYSQL_ROOT_PASSWORD: root123
      MYSQL_DATABASE: capital_core
    volumes:
      - mysql_data:/var/lib/mysql
    command: --default-authentication-plugin=mysql_native_password

  redis:
    image: redis:6.2-alpine
    container_name: redis-local
    ports:
      - "6379:6379"
    command: redis-server --requirepass redis123

  rabbitmq:
    image: rabbitmq:3.8-management
    container_name: rabbitmq-local
    ports:
      - "5672:5672"
      - "15672:15672"
    environment:
      RABBITMQ_DEFAULT_USER: admin
      RABBITMQ_DEFAULT_PASS: admin123

  eureka:
    image: springcloud/eureka
    container_name: eureka-local
    ports:
      - "8761:8761"

volumes:
  mysql_data:

# 启动命令
docker-compose up -d
```

#### 2. 配置修改

##### 2.1 创建本地配置文件
在 `capital-core/src/main/resources/` 下创建 `application-local.properties`:

```properties
# 服务配置
server.port=8001
spring.application.name=capital-core-service

# 数据库配置
spring.datasource.url=******************************************************************************************************************************************************
spring.datasource.username=root
spring.datasource.password=root123
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# JPA配置
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.database-platform=org.hibernate.dialect.MySQL8Dialect

# Redis配置
spring.data.redis.host=localhost
spring.data.redis.port=6379
spring.data.redis.password=redis123
spring.data.redis.database=0

# RabbitMQ配置
spring.rabbitmq.host=localhost
spring.rabbitmq.port=5672
spring.rabbitmq.username=admin
spring.rabbitmq.password=admin123

# Eureka配置
eureka.client.service-url.defaultZone=http://localhost:8761/eureka/
eureka.instance.prefer-ip-address=true

# 日志配置
logging.level.com.jinghang.capital=DEBUG
logging.level.org.springframework.amqp=DEBUG
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss.SSS} [%-5level][%thread] %logger{50}.%method:%line - %msg%n

# 禁用Apollo (本地开发)
apollo.bootstrap.enabled=false
spring.cloud.config.enabled=false
```

##### 2.2 修改启动类
在 `CapitalCoreApplication.java` 中添加本地配置：

```java
@SpringBootApplication
@EnableFeignClients(basePackages = { "com.jinghang.capital.core.service.remote"})
public class CapitalCoreApplication {
    public static void main(String[] args) {
        // 本地开发时设置profile
        System.setProperty("spring.profiles.active", "local");
        SpringApplication.run(CapitalCoreApplication.class, args);
    }
}
```

#### 3. 数据库初始化

```sql
-- 创建数据库
CREATE DATABASE capital_core DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户 (可选)
CREATE USER 'capital_user'@'localhost' IDENTIFIED BY 'capital123';
GRANT ALL PRIVILEGES ON capital_core.* TO 'capital_user'@'localhost';
FLUSH PRIVILEGES;
```

#### 4. 启动服务

```bash
# 1. 启动中间件
docker-compose up -d

# 2. 编译项目
mvn clean compile

# 3. 启动 capital-core
cd capital-core
mvn spring-boot:run -Dspring-boot.run.profiles=local

# 4. 启动 capital-batch (可选)
cd ../capital-batch
mvn spring-boot:run -Dspring-boot.run.profiles=local
```

### 方案二：混合环境 (使用远程Apollo)

#### 1. 保留Apollo配置
修改 `capital-core/src/main/resources/META-INF/app.properties`:

```properties
app.id=capital-core-local
apollo.env=DEV
apollo.meta=http://************:8080
```

#### 2. 在Apollo中配置本地环境
在Apollo配置中心添加 `capital-core-local` 应用，配置本地数据库和中间件连接信息。

#### 3. 本地只启动必要服务
```bash
# 只启动数据库和缓存
docker run -d --name mysql-local -p 3306:3306 -e MYSQL_ROOT_PASSWORD=root123 mysql:8.0
docker run -d --name redis-local -p 6379:6379 redis:6.2-alpine
```

### 方案三：最小化启动 (仅核心功能)

#### 1. 禁用非必要组件
创建 `application-minimal.properties`:

```properties
# 基础配置
server.port=8001
spring.application.name=capital-core-service

# 数据库 (使用H2内存数据库)
spring.datasource.url=jdbc:h2:mem:testdb
spring.datasource.driver-class-name=org.h2.Driver
spring.h2.console.enabled=true

# 禁用组件
spring.cloud.discovery.enabled=false
spring.rabbitmq.listener.simple.auto-startup=false
eureka.client.enabled=false
apollo.bootstrap.enabled=false

# 日志
logging.level.com.jinghang.capital=DEBUG
```

#### 2. 添加H2依赖
在 `capital-core/pom.xml` 中添加：

```xml
<dependency>
    <groupId>com.h2database</groupId>
    <artifactId>h2</artifactId>
    <scope>runtime</scope>
</dependency>
```

## 🔧 开发调试技巧

### 1. IDE配置
- **IDEA**: 配置 Spring Boot 运行配置，设置 Active profiles 为 `local`
- **Eclipse**: 在 Run Configuration 中设置 VM arguments: `-Dspring.profiles.active=local`

### 2. 热部署
项目已包含 `spring-boot-devtools`，修改代码后自动重启。

### 3. API测试
- **Swagger UI**: http://localhost:8001/swagger-ui.html
- **Actuator**: http://localhost:8001/actuator/health

### 4. 数据库管理
- **H2 Console**: http://localhost:8001/h2-console (方案三)
- **MySQL**: 使用 Navicat、DBeaver 等工具连接

## 🚨 常见问题解决

### 1. 端口冲突
```bash
# 查看端口占用
netstat -ano | findstr :8001
# 或
lsof -i :8001

# 修改端口
server.port=8002
```

### 2. 数据库连接失败
```bash
# 检查MySQL状态
docker ps | grep mysql
docker logs mysql-local

# 测试连接
mysql -h localhost -P 3306 -u root -p
```

### 3. Redis连接失败
```bash
# 检查Redis状态
docker ps | grep redis
redis-cli -h localhost -p 6379 ping
```

### 4. 依赖下载失败
```bash
# 清理并重新下载
mvn clean
mvn dependency:resolve
```

## 📝 启动检查清单

- [ ] Java 17 环境
- [ ] Maven 配置正确
- [ ] 数据库服务启动
- [ ] Redis 服务启动
- [ ] 配置文件正确
- [ ] 端口未被占用
- [ ] 依赖下载完成
- [ ] 服务启动成功
- [ ] 健康检查通过

## 🎯 推荐启动顺序

1. **基础环境**: Java、Maven
2. **数据存储**: MySQL、Redis
3. **服务注册**: Eureka (可选)
4. **消息队列**: RabbitMQ (可选)
5. **应用服务**: capital-core
6. **批处理服务**: capital-batch (可选)

选择适合你的方案开始本地开发吧！🚀
